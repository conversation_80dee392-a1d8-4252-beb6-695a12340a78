/**
 * WebSocket Hook for Real-time Server Connection
 * Manages connection status and provides real-time updates
 */
import { useState, useEffect, useRef, useCallback } from 'react';

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

interface WebSocketMessage {
    type: string;
    data: any;
    timestamp?: string;
    client_id?: string;
}

interface UseWebSocketOptions {
    url?: string;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    onMessage?: (message: WebSocketMessage) => void;
    onStatusChange?: (status: ConnectionStatus) => void;
}

interface UseWebSocketReturn {
    status: ConnectionStatus;
    isConnected: boolean;
    lastMessage: WebSocketMessage | null;
    sendMessage: (message: any) => void;
    connect: () => void;
    disconnect: () => void;
    reconnect: () => void;
}

export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
    const {
        url = '/ws',
        reconnectInterval = 3000,
        maxReconnectAttempts = 5,
        onMessage,
        onStatusChange
    } = options;

    const [status, setStatus] = useState<ConnectionStatus>('disconnected');
    const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
    
    const wsRef = useRef<WebSocket | null>(null);
    const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const reconnectAttemptsRef = useRef(0);
    const isManualDisconnectRef = useRef(false);

    const updateStatus = useCallback((newStatus: ConnectionStatus) => {
        setStatus(newStatus);
        onStatusChange?.(newStatus);
    }, [onStatusChange]);

    const connect = useCallback(() => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            return;
        }

        updateStatus('connecting');
        isManualDisconnectRef.current = false;

        try {
            // Create WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = url.startsWith('ws') ? url : `${protocol}//${window.location.host}${url}`;
            
            wsRef.current = new WebSocket(wsUrl);

            wsRef.current.onopen = () => {
                console.log('🔌 WebSocket connected');
                updateStatus('connected');
                reconnectAttemptsRef.current = 0;
                
                // Send initial ping
                if (wsRef.current?.readyState === WebSocket.OPEN) {
                    wsRef.current.send(JSON.stringify({ type: 'ping' }));
                }
            };

            wsRef.current.onmessage = (event) => {
                try {
                    const message: WebSocketMessage = JSON.parse(event.data);
                    setLastMessage(message);
                    onMessage?.(message);
                    
                    // Handle pong response
                    if (message.type === 'pong') {
                        console.log('💓 Received pong from server');
                    }
                } catch (error) {
                    console.error('❌ Error parsing WebSocket message:', error);
                }
            };

            wsRef.current.onclose = (event) => {
                console.log('🔌 WebSocket disconnected:', event.code, event.reason);
                
                if (!isManualDisconnectRef.current) {
                    updateStatus('disconnected');
                    
                    // Attempt reconnection
                    if (reconnectAttemptsRef.current < maxReconnectAttempts) {
                        reconnectAttemptsRef.current++;
                        console.log(`🔄 Reconnecting... (${reconnectAttemptsRef.current}/${maxReconnectAttempts})`);
                        
                        reconnectTimeoutRef.current = setTimeout(() => {
                            connect();
                        }, reconnectInterval);
                    } else {
                        console.log('❌ Max reconnection attempts reached');
                        updateStatus('error');
                    }
                }
            };

            wsRef.current.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
                updateStatus('error');
            };

        } catch (error) {
            console.error('❌ Error creating WebSocket:', error);
            updateStatus('error');
        }
    }, [url, updateStatus, onMessage, reconnectInterval, maxReconnectAttempts]);

    const disconnect = useCallback(() => {
        isManualDisconnectRef.current = true;
        
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
        }
        
        if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
        }
        
        updateStatus('disconnected');
    }, [updateStatus]);

    const reconnect = useCallback(() => {
        disconnect();
        setTimeout(() => {
            reconnectAttemptsRef.current = 0;
            connect();
        }, 100);
    }, [disconnect, connect]);

    const sendMessage = useCallback((message: any) => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify(message));
        } else {
            console.warn('⚠️ WebSocket not connected, cannot send message');
        }
    }, []);

    // Auto-connect on mount
    useEffect(() => {
        connect();
        
        return () => {
            disconnect();
        };
    }, [connect, disconnect]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
            }
        };
    }, []);

    return {
        status,
        isConnected: status === 'connected',
        lastMessage,
        sendMessage,
        connect,
        disconnect,
        reconnect
    };
};
